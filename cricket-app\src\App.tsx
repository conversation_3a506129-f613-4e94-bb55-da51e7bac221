import React, { useState, useEffect } from 'react';
import { Sun, Moon } from 'lucide-react';
import { Sidebar } from './components/Sidebar';
import { ResultCard } from './components/ResultCard';
import { MatchData } from './types';
import { 
  calculateCutoffTimes, 
  calculateDelayedStart, 
  calculateInterruption1st, 
  calculateInterruption2nd 
} from './calculations';
import { timeToMins, minsToTime } from './utils';

const initialData: MatchData = {
  teams: 'Team A vs Team B',
  startTime: '15:00',
  cutoffScenario: 'game',
  
  // Delayed Start
  ds_A: 170,
  ds_C: 0,
  ds_D: 30,
  ds_E: 0,
  ds_interval_minutes: 10,
  
  // Interruption 1st Innings
  t20_scenario: 'interruption',
  i1_AA: 170,
  i1_BB: 0,
  i1_CC: 0,
  i1_DD: 30,
  i1_EE: 0,
  
  // Terminate scenario
  term_P: '17:00',
  term_Q: '18:00',
  term_R: 60,
  term_S: 14,
  term_T: 0,
  
  // Interruption 2nd Innings
  i2_starttime: '16:45',
  i2_length: 90,
  i2_CCC: 0,
  i2_DDD: '17:15',
  i2_EEE: 0,
  i2_FFF: 0,
  i2_HHH: 20,
};

function App() {
  const [data, setData] = useState<MatchData>(initialData);
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Initialize dark mode from localStorage or system preference
  useEffect(() => {
    const savedTheme = localStorage.getItem('color-theme');
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
      setIsDarkMode(true);
      document.documentElement.classList.add('dark');
    } else {
      setIsDarkMode(false);
      document.documentElement.classList.remove('dark');
    }
  }, []);

  // Toggle dark mode
  const toggleDarkMode = () => {
    const newDarkMode = !isDarkMode;
    setIsDarkMode(newDarkMode);
    
    if (newDarkMode) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('color-theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('color-theme', 'light');
    }
  };

  // Update calculated fields when data changes
  useEffect(() => {
    const updatedData = { ...data };
    
    // Calculate ds_E
    if (data.ds_C > data.ds_D) {
      updatedData.ds_E = Math.min(10, data.ds_C - data.ds_D);
    } else {
      updatedData.ds_E = 0;
    }
    
    // Calculate i1_EE
    if (data.i1_CC > data.i1_DD) {
      updatedData.i1_EE = Math.min(10, data.i1_CC - data.i1_DD);
    } else {
      updatedData.i1_EE = 0;
    }
    
    // Calculate terminate scenario fields
    const p_mins = timeToMins(data.term_P);
    const q_mins = timeToMins(data.term_Q);
    updatedData.term_R = q_mins - p_mins;
    updatedData.term_S = Math.ceil(updatedData.term_R / 4.25);
    
    if (JSON.stringify(updatedData) !== JSON.stringify(data)) {
      setData(updatedData);
    }
  }, [data.ds_C, data.ds_D, data.i1_CC, data.i1_DD, data.term_P, data.term_Q]);

  // Calculate scheduled hours of play
  const startTimeMins = timeToMins(data.startTime);
  const firstInnsEnd = minsToTime(startTimeMins + 90);
  const secondInnsStart = minsToTime(startTimeMins + 90 + 20);
  const secondInnsEnd = minsToTime(startTimeMins + 90 + 20 + 90);

  // Calculate results
  const cutoffResult = calculateCutoffTimes(data);
  const delayedStartResult = calculateDelayedStart(data);
  const interruption1stResult = calculateInterruption1st(data);
  const interruption2ndResult = calculateInterruption2nd(data);

  return (
    <div className="bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200 min-h-screen">
      <div className="flex flex-col lg:flex-row min-h-screen">
        <Sidebar data={data} onDataChange={setData} />
        
        <main className="flex-1 p-6 lg:p-10">
          <div className="flex justify-end mb-4">
            <button
              onClick={toggleDarkMode}
              className="p-2 rounded-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            >
              {isDarkMode ? (
                <Sun className="w-6 h-6 text-yellow-500" />
              ) : (
                <Moon className="w-6 h-6 text-gray-600" />
              )}
            </button>
          </div>

          {/* Match Information Display */}
          <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/50 rounded-lg">
            <p><b>Scheduled Hours of Play:</b></p>
            <p><b>First Innings:</b> {data.startTime} - {firstInnsEnd}</p>
            <p><b>Interval:</b> 20 minutes</p>
            <p><b>Second Innings:</b> {secondInnsStart} - {secondInnsEnd}</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-2 gap-6">
            <ResultCard
              id="cutoff-card"
              title="Scheduled HoP: Cut-off Times"
              titleColor="#0E46A3"
              result={cutoffResult}
              teams={data.teams}
              filename="Cutoff-Times"
            />
            
            <ResultCard
              id="delayed-start-card"
              title="Rescheduled HoP: Delayed Start"
              titleColor="#1E0342"
              result={delayedStartResult}
              teams={data.teams}
              filename="Delayed-Start-HoP"
            />
            
            <ResultCard
              id="interruption-1st-card"
              title="Rescheduled HoP: Interruption 1st Innings"
              titleColor="#0E46A3"
              result={interruption1stResult}
              teams={data.teams}
              filename="Interruption-1st-Inns-HoP"
            />
            
            <ResultCard
              id="interruption-2nd-card"
              title="Rescheduled HoP: Interruption 2nd Innings"
              titleColor="#8576FF"
              result={interruption2ndResult}
              teams={data.teams}
              filename="Interruption-2nd-Inns-HoP"
            />
          </div>
        </main>
      </div>
    </div>
  );
}

export default App;
