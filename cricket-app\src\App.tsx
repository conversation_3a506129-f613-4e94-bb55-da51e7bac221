import React from "react";

function App() {
  console.log("App component is rendering!");

  return (
    <div
      style={{
        minHeight: "100vh",
        backgroundColor: "#3b82f6",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        color: "white",
        fontFamily: "Arial, sans-serif",
      }}
    >
      <div style={{ textAlign: "center" }}>
        <h1
          style={{ fontSize: "2rem", fontWeight: "bold", marginBottom: "1rem" }}
        >
          T20 Cricket Calculator
        </h1>
        <p style={{ fontSize: "1.2rem", marginBottom: "1rem" }}>
          React App is Working!
        </p>
        <div
          style={{
            marginTop: "1rem",
            padding: "1rem",
            backgroundColor: "white",
            color: "#3b82f6",
            borderRadius: "8px",
          }}
        >
          <p>If you can see this, React is working correctly.</p>
          <p>Current time: {new Date().toLocaleTimeString()}</p>
        </div>
      </div>
    </div>
  );
}

export default App;
