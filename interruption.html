<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>T20 Revised Overs Calculator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <style>
      body {
        font-family: "Inter", sans-serif;
      }
      .accordion-content {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-out;
      }
      .accordion-button.active + .accordion-content {
        max-height: 1000px; /* Adjust as needed */
      }
      .card {
        background-color: white;
        border-radius: 0.75rem;
        box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1),
          0 2px 4px -2px rgb(0 0 0 / 0.1);
        transition: all 0.3s ease;
      }
      .dark .card {
        background-color: #1f2937;
      }
    </style>
  </head>
  <body class="bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200">
    <div class="flex flex-col lg:flex-row min-h-screen">
      <!-- Sidebar -->
      <aside
        class="w-full lg:w-1/3 xl:w-1/4 bg-white dark:bg-gray-800 p-6 shadow-lg"
      >
        <div class="flex items-center mb-6">
          <img
            src="https://placehold.co/80x80/0E46A3/white?text=MT"
            alt="Logo"
            class="rounded-full"
          />
          <h1 class="text-xl font-bold ml-4">T20 Revised Overs Calculator</h1>
        </div>
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">
          This app helps in calculating revised overs in a Delayed or
          Interrupted game during Maharaja Trophy.
        </p>

        <div id="accordion-container">
          <!-- Accordion items will be generated here by JS -->
        </div>
      </aside>

      <!-- Main Content -->
      <main class="w-full lg:w-2/3 xl:w-3/4 p-6 lg:p-10">
        <div class="flex justify-end mb-4">
          <button
            id="dark-mode-toggle"
            class="p-2 rounded-full bg-gray-200 dark:bg-gray-700"
          >
            <svg
              id="sun-icon"
              class="w-6 h-6 text-yellow-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
              ></path>
            </svg>
            <svg
              id="moon-icon"
              class="w-6 h-6 text-gray-400 hidden"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
              ></path>
            </svg>
          </button>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-2 gap-6">
          <!-- Result Cards -->
          <div id="cutoff-card" class="card p-6"></div>
          <div id="delayed-start-card" class="card p-6"></div>
          <div id="interruption-1st-card" class="card p-6"></div>
          <div id="interruption-2nd-card" class="card p-6"></div>
        </div>
        <div class="mt-6">
          <div
            id="delayed-start-table-card"
            class="card p-6 overflow-x-auto"
          ></div>
        </div>
      </main>
    </div>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Dark Mode Toggle
        const darkModeToggle = document.getElementById("dark-mode-toggle");
        const sunIcon = document.getElementById("sun-icon");
        const moonIcon = document.getElementById("moon-icon");

        if (
          localStorage.getItem("color-theme") === "dark" ||
          (!("color-theme" in localStorage) &&
            window.matchMedia("(prefers-color-scheme: dark)").matches)
        ) {
          document.documentElement.classList.add("dark");
          sunIcon.classList.add("hidden");
          moonIcon.classList.remove("hidden");
        } else {
          document.documentElement.classList.remove("dark");
          sunIcon.classList.remove("hidden");
          moonIcon.classList.add("hidden");
        }

        darkModeToggle.addEventListener("click", function () {
          document.documentElement.classList.toggle("dark");
          sunIcon.classList.toggle("hidden");
          moonIcon.classList.toggle("hidden");

          if (document.documentElement.classList.contains("dark")) {
            localStorage.setItem("color-theme", "dark");
          } else {
            localStorage.setItem("color-theme", "light");
          }
        });

        // Accordion Logic
        const accordionContainer = document.getElementById(
          "accordion-container"
        );
        const accordionData = [
          {
            title: "Match Information",
            icon: "ℹ️",
            content: `
                        <label class="block text-sm font-medium mb-2" for="teams">Teams for the match:</label>
                        <input type="text" id="teams" class="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 border-gray-300 dark:border-gray-600 mb-4" value="Team A vs Team B">
                        <label class="block text-sm font-medium mb-2" for="starttime">Start Time of the match:</label>
                        <input type="time" id="starttime" class="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 border-gray-300 dark:border-gray-600 mb-4" value="15:00">
                        <div id="innings_output" class="text-sm space-y-1 mt-4 p-3 bg-blue-50 dark:bg-blue-900/50 rounded-lg"></div>
                    `,
          },
          {
            title: "Cut-off time",
            icon: "⏰",
            content: `
                        <label class="block text-sm font-medium mb-2">Select Cut-Off Type:</label>
                        <div class="space-y-2">
                            <div>
                                <input type="radio" id="cutoff_game" name="cutoff_scenario" value="game" checked class="mr-2">
                                <label for="cutoff_game">To start the game: Minimum 5 overs a side</label>
                            </div>
                            <div>
                                <input type="radio" id="cutoff_2nd" name="cutoff_scenario" value="2nd_innings" class="mr-2">
                                <label for="cutoff_2nd">To start the 2nd innings: Minimum 5 overs</label>
                            </div>
                        </div>
                    `,
          },
          {
            title: "Delayed Start",
            icon: "⏳",
            content: `
                        <div class="grid grid-cols-2 gap-4">
                            <div><label for="ds_A" class="text-sm">Net playing time (A)</label><input type="number" id="ds_A" value="170" class="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"></div>
                            <div><label for="ds_C" class="text-sm">Playing time lost (C)</label><input type="number" id="ds_C" value="0" class="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"></div>
                            <div><label for="ds_D" class="text-sm">Extra time available (D)</label><input type="number" id="ds_D" value="30" class="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"></div>
                            <div><label for="ds_E" class="text-sm">Time made up (E)</label><input type="number" id="ds_E" value="0" class="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700" readonly></div>
                        </div>
                        <div id="delayed_start_time_output" class="text-sm mt-4 font-semibold"></div>
                        <hr class="my-4 border-gray-300 dark:border-gray-600">
                        <h4 class="font-semibold mb-2">Delayed Start Time Interval Table</h4>
                        <div><label for="ds_interval_minutes" class="text-sm">Interval (minutes)</label><input type="number" id="ds_interval_minutes" value="10" class="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"></div>

                    `,
          },
          {
            title: "Interruption: 1st Innings",
            icon: "🌧️",
            content: `
                        <div class="space-y-2 mb-4">
                            <div>
                                <input type="radio" id="interruption_1st" name="t20_scenario" value="interruption" checked class="mr-2">
                                <label for="interruption_1st">Interruption during 1st innings</label>
                            </div>
                            <div>
                                <input type="radio" id="interruption_terminate" name="t20_scenario" value="terminate" class="mr-2">
                                <label for="interruption_terminate">Terminate the innings</label>
                            </div>
                        </div>
                        <div id="interruption-1st-panel">
                            <div class="grid grid-cols-2 gap-4">
                                <div><label for="i1_AA" class="text-sm">Net playing time (A)</label><input type="number" id="i1_AA" value="170" class="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"></div>
                                <div><label for="i1_BB" class="text-sm">Time innings in progress (B)</label><input type="number" id="i1_BB" value="0" class="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"></div>
                                <div><label for="i1_CC" class="text-sm">Playing time lost (C)</label><input type="number" id="i1_CC" value="0" class="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"></div>
                                <div><label for="i1_DD" class="text-sm">Extra time available (D)</label><input type="number" id="i1_DD" value="30" class="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"></div>
                                <div><label for="i1_EE" class="text-sm">Time made up (E)</label><input type="number" id="i1_EE" value="0" class="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700" readonly></div>
                            </div>
                            <div id="timeone_output" class="text-sm mt-4 font-semibold"></div>
                        </div>
                        <div id="terminate-panel" class="hidden">
                             <div class="grid grid-cols-2 gap-4">
                                <div><label for="term_P" class="text-sm">Proposed re-start (P)</label><input type="time" id="term_P" value="17:00" class="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"></div>
                                <div><label for="term_Q" class="text-sm">Rescheduled cut-off (Q)</label><input type="time" id="term_Q" value="18:00" class="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"></div>
                                <div><label for="term_R" class="text-sm">Mins between P & Q (R)</label><input type="number" id="term_R" value="60" class="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700" readonly></div>
                                <div><label for="term_S" class="text-sm">Potential overs (S)</label><input type="number" id="term_S" value="14" class="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700" readonly></div>
                                <div><label for="term_T" class="text-sm">Overs faced in 1st inns (T)</label><input type="number" id="term_T" value="0" class="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"></div>
                            </div>
                        </div>
                    `,
          },
          {
            title: "Interruption: 2nd Innings",
            icon: "🌧️",
            content: `
                        <div class="grid grid-cols-2 gap-4">
                             <div><label for="i2_starttime" class="text-sm">2nd Inns Start Time</label><input type="time" id="i2_starttime" value="16:45" class="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"></div>
                             <div><label for="i2_length" class="text-sm">Length of 2nd Inns</label><input type="number" id="i2_length" value="90" class="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"></div>
                        </div>
                        <div id="innings2nd_output" class="text-sm space-y-1 my-4 p-3 bg-blue-50 dark:bg-blue-900/50 rounded-lg"></div>
                         <div class="grid grid-cols-2 gap-4">
                            <div><label for="i2_CCC" class="text-sm">Time innings in progress (C)</label><input type="number" id="i2_CCC" value="0" class="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"></div>
                            <div><label for="i2_DDD" class="text-sm">Restart time (D)</label><input type="time" id="i2_DDD" value="17:15" class="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"></div>
                            <div><label for="i2_EEE" class="text-sm">Total playing time lost (E)</label><input type="number" id="i2_EEE" value="0" class="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"></div>
                            <div><label for="i2_FFF" class="text-sm">Additional time available (F)</label><input type="number" id="i2_FFF" value="0" class="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"></div>
                            <div><label for="i2_HHH" class="text-sm">Max overs at start of inns (H)</label><input type="number" id="i2_HHH" value="20" class="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"></div>
                         </div>
                    `,
          },
        ];

        accordionData.forEach((item, index) => {
          const accItem = document.createElement("div");
          accItem.className = "border-b border-gray-200 dark:border-gray-700";
          accItem.innerHTML = `
                    <button class="accordion-button w-full flex justify-between items-center p-4 text-left font-semibold hover:bg-gray-100 dark:hover:bg-gray-700 transition">
                        <span>${item.icon} ${item.title}</span>
                        <span class="transform transition-transform duration-300 ">▼</span>
                    </button>
                    <div class="accordion-content bg-gray-50 dark:bg-gray-800/50 p-4">
                        ${item.content}
                    </div>
                `;
          accordionContainer.appendChild(accItem);
        });

        const accordionButtons = document.querySelectorAll(".accordion-button");
        accordionButtons.forEach((button) => {
          button.addEventListener("click", () => {
            const currentlyActive = document.querySelector(
              ".accordion-button.active"
            );
            if (currentlyActive && currentlyActive !== button) {
              currentlyActive.classList.remove("active");
              currentlyActive.querySelector("span:last-child").style.transform =
                "rotate(0deg)";
            }

            button.classList.toggle("active");
            const icon = button.querySelector("span:last-child");
            if (button.classList.contains("active")) {
              icon.style.transform = "rotate(180deg)";
            } else {
              icon.style.transform = "rotate(0deg)";
            }
          });
        });

        // Radio button logic for 1st innings interruption
        const t20ScenarioRadios = document.querySelectorAll(
          'input[name="t20_scenario"]'
        );
        const interruptionPanel = document.getElementById(
          "interruption-1st-panel"
        );
        const terminatePanel = document.getElementById("terminate-panel");

        t20ScenarioRadios.forEach((radio) => {
          radio.addEventListener("change", () => {
            if (radio.value === "interruption") {
              interruptionPanel.classList.remove("hidden");
              terminatePanel.classList.add("hidden");
            } else {
              interruptionPanel.classList.add("hidden");
              terminatePanel.classList.remove("hidden");
            }
            calculateAll();
          });
        });

        // --- Calculation Logic ---
        const OVER_RATE = 4.25;

        function timeToMins(timeStr) {
          if (!timeStr) return 0;
          const [h, m] = timeStr.split(":").map(Number);
          return h * 60 + m;
        }

        function minsToTime(mins) {
          if (isNaN(mins)) return "00:00";
          const h = Math.floor(mins / 60) % 24;
          const m = Math.round(mins % 60);
          return `${String(h).padStart(2, "0")}:${String(m).padStart(2, "0")}`;
        }

        function calculatePowerplay(overs) {
          if (overs >= 19) return 6;
          if (overs >= 15) return 5;
          if (overs >= 12) return 4;
          if (overs >= 9) return 3;
          return 2;
        }

        function calculateAll() {
          // Match Info
          const startTime = document.getElementById("starttime").value;
          const startTimeMins = timeToMins(startTime);
          const firstInnsEnd = minsToTime(startTimeMins + 90);
          const secondInnsStart = minsToTime(startTimeMins + 90 + 20);
          const secondInnsEnd = minsToTime(startTimeMins + 90 + 20 + 90);

          document.getElementById("innings_output").innerHTML = `
                    <p><b>Scheduled Hours of Play:</b></p>
                    <p><b>First Innings:</b> ${startTime} - ${firstInnsEnd}</p>
                    <p><b>Interval:</b> 20 minutes</p>
                    <p><b>Second Innings:</b> ${secondInnsStart} - ${secondInnsEnd}</p>
                `;

          // Cut-off Time
          const matchEndTime = startTimeMins + 90 + 20 + 90;
          const matchCutoffTime = matchEndTime + 30; // Extra time
          const min5OverTime = Math.ceil(5 * OVER_RATE);
          const totalMinGameTime = min5OverTime + 10 + min5OverTime;

          const cutoffStartGameMins = matchCutoffTime - totalMinGameTime;
          const cutoff2ndInnsMins = matchCutoffTime - min5OverTime;

          const cutoffScenario = document.querySelector(
            'input[name="cutoff_scenario"]:checked'
          ).value;
          let cutoffHTML = `
                    <h3 class="text-lg font-bold mb-2" style="color: #0E46A3;">Scheduled HoP: Cut-off Times</h3>
                `;

          if (cutoffScenario === "game") {
            const firstInnsStartC = minsToTime(cutoffStartGameMins);
            const firstInnsEndC = minsToTime(
              cutoffStartGameMins + min5OverTime
            );
            const secondInnsStartC = minsToTime(
              cutoffStartGameMins + min5OverTime + 10
            );
            const secondInnsEndC = minsToTime(
              cutoffStartGameMins + min5OverTime + 10 + min5OverTime
            );
            cutoffHTML += `
                        <p class="text-green-500 font-semibold">Cut off to start game (5 overs/side): ${firstInnsStartC}</p>
                        <div class="text-sm mt-2 space-y-1">
                            <p><b>Start Time:</b> ${firstInnsStartC}</p>
                            <p><b>Max Overs:</b> 5</p>
                            <p><b>First Innings:</b> ${firstInnsStartC} - ${firstInnsEndC}</p>
                            <p><b>Interval:</b> 10 Minutes</p>
                            <p><b>Second Innings:</b> ${secondInnsStartC} - ${secondInnsEndC}</p>
                            <p><b>Max Overs/Bowler:</b> 2</p>
                            <p><b>Powerplay:</b> ${calculatePowerplay(
                              5
                            )} Overs</p>
                        </div>`;
          } else {
            const secondInnsStartC = minsToTime(cutoff2ndInnsMins);
            const secondInnsEndC = minsToTime(cutoff2ndInnsMins + min5OverTime);
            cutoffHTML += `
                        <p class="text-green-500 font-semibold">Cut off to start 2nd innings (5 overs): ${secondInnsStartC}</p>
                        <div class="text-sm mt-2 space-y-1">
                            <p><b>Restart time:</b> ${secondInnsStartC}</p>
                            <p><b>Max Overs:</b> 5</p>
                            <p><b>Second Innings:</b> ${secondInnsStartC} - ${secondInnsEndC}</p>
                            <p><b>Max Overs/Bowler:</b> 2</p>
                            <p><b>Powerplay:</b> ${calculatePowerplay(
                              5
                            )} Overs</p>
                        </div>`;
          }
          cutoffHTML += `<button onclick="downloadPDF('cutoff-card', 'Cutoff-Times')" class="mt-4 bg-blue-600 text-white px-3 py-1 rounded-full text-xs hover:bg-blue-700">Download HoP</button>`;
          document.getElementById("cutoff-card").innerHTML = cutoffHTML;

          // Delayed Start
          const ds_C = Number(document.getElementById("ds_C").value);
          const ds_D = Number(document.getElementById("ds_D").value);
          const ds_A = Number(document.getElementById("ds_A").value);

          let ds_E_val = 0;
          if (ds_C > ds_D) {
            ds_E_val = Math.min(10, ds_C - ds_D);
          }
          document.getElementById("ds_E").value = ds_E_val;

          const ds_F = ds_C - (ds_D + ds_E_val); // Effective time lost
          const ds_G = ds_A - ds_F; // Remaining playing time
          const ds_H = ds_G / OVER_RATE;
          const ds_I = Math.min(20, Math.ceil(ds_H / 2)); // Max overs per team

          let dsHTML = `<h3 class="text-lg font-bold mb-2" style="color: #1E0342;">Rescheduled HoP: Delayed Start</h3>`;
          if (ds_I < 5) {
            dsHTML += `<p class="text-red-500 font-bold">Minimum 5 overs not possible.</p>`;
          } else {
            const ds_J = ds_I > 9 ? Math.ceil(ds_I / 5) : 2; // Max overs per bowler
            const ds_L = Math.ceil(ds_I * OVER_RATE); // Innings length
            const ds_K_mins = startTimeMins + ds_C;
            const ds_M_mins = ds_K_mins + ds_L;
            const ds_N = 20 - ds_E_val; // Interval
            const ds_O_mins = ds_M_mins + ds_N;
            const ds_P_mins = ds_O_mins + ds_L;

            dsHTML += `
                        <div class="text-sm space-y-1">
                            <p><b>Start Time:</b> ${minsToTime(ds_K_mins)}</p>
                            <p><b>Max overs/team:</b> ${ds_I}</p>
                            <p><b>1st Innings:</b> ${minsToTime(
                              ds_K_mins
                            )} - ${minsToTime(ds_M_mins)}</p>
                            <p><b>Interval:</b> ${ds_N} minutes</p>
                            <p><b>2nd Innings:</b> ${minsToTime(
                              ds_O_mins
                            )} - ${minsToTime(ds_P_mins)}</p>
                            <p><b>Max overs/bowler:</b> ${ds_J}</p>
                            <p><b>Powerplay:</b> ${calculatePowerplay(ds_I)}</p>
                        </div>
                        <button onclick="downloadPDF('delayed-start-card', 'Delayed-Start-HoP')" class="mt-4 bg-blue-600 text-white px-3 py-1 rounded-full text-xs hover:bg-blue-700">Download HoP</button>
                    `;
            document.getElementById(
              "delayed_start_time_output"
            ).innerHTML = `First Innings to commence: ${minsToTime(ds_K_mins)}`;
          }
          document.getElementById("delayed-start-card").innerHTML = dsHTML;

          // Delayed Start Table
          const ds_interval =
            Number(document.getElementById("ds_interval_minutes").value) || 10;
          let tableHTML = `<h3 class="text-lg font-bold mb-4" style="color: #003C43;">Reschedule HoP - Delayed Start: ${ds_interval} Minutes Interval</h3>
                                 <div class="overflow-x-auto"><table class="w-full text-sm text-left">
                                 <thead class="bg-gray-200 dark:bg-gray-700"><tr><th class="p-2">Metric</th>`;
          let times = [];
          for (let i = 0; i < 5; i++) {
            const delay = i * ds_interval;
            const time = minsToTime(startTimeMins + delay);
            times.push(time);
            tableHTML += `<th class="p-2">${time}</th>`;
          }
          tableHTML += `</tr></thead><tbody>`;

          const metrics = [
            "Max Overs/Team",
            "Length of Innings",
            "1st Inns End",
            "Interval",
            "2nd Inns Start",
            "2nd Inns End",
            "Max Overs/Bowler",
            "Powerplay",
          ];
          metrics.forEach((metric) => {
            tableHTML += `<tr class="border-b dark:border-gray-700"><td class="p-2 font-semibold">${metric}</td>`;
            for (let i = 0; i < 5; i++) {
              const delay = i * ds_interval;
              let E = 0;
              if (delay > 30) E = Math.min(10, delay - 30);
              const F = delay - (30 + E);
              const G = 170 - F;
              const I = Math.min(20, Math.ceil(G / OVER_RATE / 2));

              let val = "-";
              if (I >= 5) {
                const L = Math.ceil(I * OVER_RATE);
                const K_mins = startTimeMins + delay;
                const M_mins = K_mins + L;
                const N = 20 - E;
                const O_mins = M_mins + N;
                const P_mins = O_mins + L;
                const J = I > 9 ? Math.ceil(I / 5) : 2;

                switch (metric) {
                  case "Max Overs/Team":
                    val = I;
                    break;
                  case "Length of Innings":
                    val = `${L} min`;
                    break;
                  case "1st Inns End":
                    val = minsToTime(M_mins);
                    break;
                  case "Interval":
                    val = `${N} min`;
                    break;
                  case "2nd Inns Start":
                    val = minsToTime(O_mins);
                    break;
                  case "2nd Inns End":
                    val = minsToTime(P_mins);
                    break;
                  case "Max Overs/Bowler":
                    val = J;
                    break;
                  case "Powerplay":
                    val = calculatePowerplay(I);
                    break;
                }
              }
              tableHTML += `<td class="p-2">${val}</td>`;
            }
            tableHTML += `</tr>`;
          });
          tableHTML += `</tbody></table></div>`;
          document.getElementById("delayed-start-table-card").innerHTML =
            tableHTML;

          // Interruption 1st Innings
          let i1HTML = `<h3 class="text-lg font-bold mb-2" style="color: #0E46A3;">Rescheduled HoP: Interruption 1st Innings</h3>`;
          const t20Scenario = document.querySelector(
            'input[name="t20_scenario"]:checked'
          ).value;

          if (t20Scenario === "interruption") {
            const i1_AA = Number(document.getElementById("i1_AA").value);
            const i1_BB = Number(document.getElementById("i1_BB").value);
            const i1_CC = Number(document.getElementById("i1_CC").value);
            const i1_DD = Number(document.getElementById("i1_DD").value);
            let i1_EE = 0;
            if (i1_CC > i1_DD) i1_EE = Math.min(10, i1_CC - i1_DD);
            document.getElementById("i1_EE").value = i1_EE;

            const i1_FF = i1_CC - (i1_DD + i1_EE);
            const i1_GG = i1_AA - i1_FF;
            const i1_II = Math.min(20, Math.ceil(i1_GG / OVER_RATE / 2));

            if (i1_II < 5) {
              i1HTML += `<p class="text-red-500 font-bold">Minimum 5 overs not possible.</p>`;
            } else {
              const i1_JJ = i1_II > 9 ? Math.ceil(i1_II / 5) : 2;
              const i1_LL = Math.ceil(i1_II * OVER_RATE);
              const i1_KK_mins = startTimeMins + i1_BB + i1_CC;
              const i1_MM_mins = i1_KK_mins + (i1_LL - i1_BB);
              const i1_NN = 20 - i1_EE;
              const i1_OO_mins = i1_MM_mins + i1_NN;
              const i1_PP_mins = i1_OO_mins + i1_LL;

              i1HTML += `
                            <div class="text-sm space-y-1">
                                <p><b>Restart Time:</b> ${minsToTime(
                                  i1_KK_mins
                                )}</p>
                                <p><b>Max overs/team:</b> ${i1_II}</p>
                                <p><b>1st Innings:</b> ${minsToTime(
                                  i1_KK_mins
                                )} - ${minsToTime(i1_MM_mins)}</p>
                                <p><b>Interval:</b> ${i1_NN} minutes</p>
                                <p><b>2nd Innings:</b> ${minsToTime(
                                  i1_OO_mins
                                )} - ${minsToTime(i1_PP_mins)}</p>
                                <p><b>Max overs/bowler:</b> ${i1_JJ}</p>
                                <p><b>Powerplay:</b> ${calculatePowerplay(
                                  i1_II
                                )}</p>
                            </div>`;
              document.getElementById(
                "timeone_output"
              ).innerHTML = `First Innings to recommence: ${minsToTime(
                i1_KK_mins
              )}`;
            }
          } else {
            // Terminate
            const p_mins = timeToMins(document.getElementById("term_P").value);
            const q_mins = timeToMins(document.getElementById("term_Q").value);
            const r_val = q_mins - p_mins;
            document.getElementById("term_R").value = r_val;
            const s_val = Math.ceil(r_val / OVER_RATE);
            document.getElementById("term_S").value = s_val;
            const t_val = Number(document.getElementById("term_T").value);

            if (s_val > t_val) {
              i1HTML += `<p class="text-red-500 font-bold">Potential overs > overs faced. Revert to "Interruption" calculation.</p>`;
            } else if (s_val < 5) {
              i1HTML += `<p class="text-red-500 font-bold">Minimum 5 overs not possible.</p>`;
            } else {
              const term_length = Math.ceil(s_val * OVER_RATE);
              const term_end_mins = p_mins + term_length;
              const term_bowler_max = s_val > 9 ? Math.ceil(s_val / 5) : 2;
              i1HTML += `
                            <p class="font-semibold text-amber-600 dark:text-amber-400">Terminate 1st Innings. Revised HoP for 2nd Innings:</p>
                            <div class="text-sm space-y-1 mt-2">
                                <p><b>Restart Time:</b> ${minsToTime(
                                  p_mins
                                )}</p>
                                <p><b>Maximum overs:</b> ${s_val}</p>
                                <p><b>Innings duration:</b> ${term_length} minutes</p>
                                <p><b>2nd Innings End:</b> ${minsToTime(
                                  term_end_mins
                                )}</p>
                                <p><b>Max overs/bowler:</b> ${term_bowler_max}</p>
                                <p><b>Powerplay:</b> ${calculatePowerplay(
                                  s_val
                                )}</p>
                            </div>`;
            }
          }
          i1HTML += `<button onclick="downloadPDF('interruption-1st-card', 'Interruption-1st-Inns-HoP')" class="mt-4 bg-blue-600 text-white px-3 py-1 rounded-full text-xs hover:bg-blue-700">Download HoP</button>`;
          document.getElementById("interruption-1st-card").innerHTML = i1HTML;

          // Interruption 2nd Innings
          const i2_start_time = document.getElementById("i2_starttime").value;
          const i2_length = Number(document.getElementById("i2_length").value);
          document.getElementById("innings2nd_output").innerHTML = `
                    <p><b>Second Innings:</b> ${i2_start_time} - ${minsToTime(
            timeToMins(i2_start_time) + i2_length
          )}</p>
                `;

          const i2_CCC = Number(document.getElementById("i2_CCC").value);
          const i2_DDD_mins = timeToMins(
            document.getElementById("i2_DDD").value
          );
          const i2_EEE = Number(document.getElementById("i2_EEE").value);
          const i2_FFF = Number(document.getElementById("i2_FFF").value);
          const i2_HHH = Number(document.getElementById("i2_HHH").value);

          const i2_GGG = i2_EEE - i2_FFF; // Net time lost
          const i2_III = Math.max(0, Math.floor(i2_GGG / OVER_RATE)); // Overs lost
          const i2_JJJ = Math.min(20, i2_HHH - i2_III); // Adjusted max overs

          let i2HTML = `<h3 class="text-lg font-bold mb-2" style="color: #8576FF;">Rescheduled HoP: Interruption 2nd Innings</h3>`;
          if (i2_JJJ < 5) {
            i2HTML += `<p class="text-red-500 font-bold">Minimum 5 overs not possible.</p>`;
          } else {
            const i2_KKK = Math.ceil(i2_JJJ * OVER_RATE); // Rescheduled length
            const i2_LLL_mins = i2_DDD_mins + (i2_KKK - i2_CCC);
            const i2_MMM = i2_JJJ > 9 ? Math.ceil(i2_JJJ / 5) : 2;

            i2HTML += `
                        <div class="text-sm space-y-1">
                            <p><b>Restart Time:</b> ${minsToTime(
                              i2_DDD_mins
                            )}</p>
                            <p><b>Overs lost:</b> ${i2_III}</p>
                            <p><b>Adjusted max overs:</b> ${i2_JJJ}</p>
                            <p><b>Amended cessation time:</b> ${minsToTime(
                              i2_LLL_mins
                            )}</p>
                            <p><b>Max overs/bowler:</b> ${i2_MMM}</p>
                            <p><b>Powerplay:</b> ${calculatePowerplay(
                              i2_JJJ
                            )}</p>
                        </div>
                        <button onclick="downloadPDF('interruption-2nd-card', 'Interruption-2nd-Inns-HoP')" class="mt-4 bg-blue-600 text-white px-3 py-1 rounded-full text-xs hover:bg-blue-700">Download HoP</button>
                    `;
          }
          document.getElementById("interruption-2nd-card").innerHTML = i2HTML;
        }

        // Initial calculation and event listeners
        calculateAll();
        const inputs = document.querySelectorAll("input");
        inputs.forEach((input) =>
          input.addEventListener("input", calculateAll)
        );
      });

      // PDF Download Function
      function downloadPDF(cardId, filename) {
        const { jsPDF } = window.jspdf;
        const card = document.getElementById(cardId);
        const teams = document.getElementById("teams").value;
        const date = new Date().toLocaleDateString("en-GB");

        html2canvas(card, {
          scale: 2,
          backgroundColor: document.documentElement.classList.contains("dark")
            ? "#1f2937"
            : "#ffffff",
        }).then((canvas) => {
          const imgData = canvas.toDataURL("image/png");
          const pdf = new jsPDF({
            orientation: "p",
            unit: "mm",
            format: "a4",
          });

          const pdfWidth = pdf.internal.pageSize.getWidth();
          const pdfHeight = pdf.internal.pageSize.getHeight();
          const cardWidth = canvas.width;
          const cardHeight = canvas.height;
          const ratio = cardWidth / cardHeight;

          let imgWidth = pdfWidth - 20;
          let imgHeight = imgWidth / ratio;

          const yPos = 40;

          pdf.setFontSize(16);
          pdf.setFont("helvetica", "bold");
          pdf.text(`Revised Hours of Play: ${teams}`, pdfWidth / 2, 20, {
            align: "center",
          });
          pdf.setFontSize(12);
          pdf.setFont("helvetica", "normal");
          pdf.text(`Date: ${date}`, pdfWidth / 2, 28, { align: "center" });

          pdf.addImage(imgData, "PNG", 10, yPos, imgWidth, imgHeight);

          // Add dotted line and repeat content for distribution
          let currentY = yPos + imgHeight + 10;
          const repeatCount = 3;

          for (let i = 0; i < repeatCount; i++) {
            if (currentY > pdfHeight - 20 - imgHeight) {
              pdf.addPage();
              currentY = 20;
            }
            pdf.setLineDashPattern([1, 1], 0);
            pdf.line(10, currentY, pdfWidth - 10, currentY);
            currentY += 10;
            pdf.addImage(imgData, "PNG", 10, currentY, imgWidth, imgHeight);
            currentY += imgHeight + 10;
          }

          pdf.save(`${filename} ${date}.pdf`);
        });
      }
    </script>
  </body>
</html>
