import React from 'react';
import { Download } from 'lucide-react';
import { CalculationResult } from '../types';
import { downloadPDF } from '../pdfUtils';

interface ResultCardProps {
  id: string;
  title: string;
  titleColor: string;
  result: CalculationResult;
  teams: string;
  filename: string;
  children?: React.ReactNode;
}

export const ResultCard: React.FC<ResultCardProps> = ({
  id,
  title,
  titleColor,
  result,
  teams,
  filename,
  children
}) => {
  const handleDownloadPDF = () => {
    downloadPDF(id, filename, teams);
  };

  return (
    <div id={id} className="card p-6">
      <h3 className="text-lg font-bold mb-2" style={{ color: titleColor }}>
        {title}
      </h3>
      
      {!result.isValid ? (
        <p className="text-red-500 font-bold">{result.message}</p>
      ) : (
        <div className="text-sm space-y-1">
          {result.message && (
            <p className="font-semibold text-amber-600 dark:text-amber-400 mb-2">
              {result.message}
            </p>
          )}
          {result.startTime && (
            <p><b>Start Time:</b> {result.startTime}</p>
          )}
          <p><b>Max overs/team:</b> {result.maxOvers}</p>
          {result.endTime && (
            <p><b>End Time:</b> {result.endTime}</p>
          )}
          {result.interval !== undefined && (
            <p><b>Interval:</b> {result.interval} minutes</p>
          )}
          <p><b>Max overs/bowler:</b> {result.maxOversPerBowler}</p>
          <p><b>Powerplay:</b> {result.powerplayOvers} overs</p>
          {children}
        </div>
      )}
      
      <button
        onClick={handleDownloadPDF}
        className="mt-4 bg-blue-600 text-white px-3 py-1 rounded-full text-xs hover:bg-blue-700 flex items-center gap-1"
      >
        <Download size={12} />
        Download HoP
      </button>
    </div>
  );
};
