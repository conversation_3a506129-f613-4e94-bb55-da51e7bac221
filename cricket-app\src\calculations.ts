import { MatchData, CalculationResult } from './types';
import { OVER_RATE, timeToMins, minsToTime, calculatePowerplay, calculateMaxOversPerBowler } from './utils';

export function calculateCutoffTimes(data: MatchData): CalculationResult {
  const startTimeMins = timeToMins(data.startTime);
  const matchEndTime = startTimeMins + 90 + 20 + 90;
  const matchCutoffTime = matchEndTime + 30; // Extra time
  const min5OverTime = Math.ceil(5 * OVER_RATE);
  const totalMinGameTime = min5OverTime + 10 + min5OverTime;

  const cutoffStartGameMins = matchCutoffTime - totalMinGameTime;
  const cutoff2ndInnsMins = matchCutoffTime - min5OverTime;

  if (data.cutoffScenario === 'game') {
    return {
      isValid: true,
      maxOvers: 5,
      startTime: minsToTime(cutoffStartGameMins),
      endTime: minsToTime(cutoffStartGameMins + totalMinGameTime),
      interval: 10,
      maxOversPerBowler: 2,
      powerplayOvers: calculatePowerplay(5),
    };
  } else {
    return {
      isValid: true,
      maxOvers: 5,
      startTime: minsToTime(cutoff2ndInnsMins),
      endTime: minsToTime(cutoff2ndInnsMins + min5OverTime),
      maxOversPerBowler: 2,
      powerplayOvers: calculatePowerplay(5),
    };
  }
}

export function calculateDelayedStart(data: MatchData): CalculationResult {
  const startTimeMins = timeToMins(data.startTime);
  
  let ds_E_val = 0;
  if (data.ds_C > data.ds_D) {
    ds_E_val = Math.min(10, data.ds_C - data.ds_D);
  }

  const ds_F = data.ds_C - (data.ds_D + ds_E_val); // Effective time lost
  const ds_G = data.ds_A - ds_F; // Remaining playing time
  const ds_H = ds_G / OVER_RATE;
  const ds_I = Math.min(20, Math.ceil(ds_H / 2)); // Max overs per team

  if (ds_I < 5) {
    return {
      isValid: false,
      maxOvers: ds_I,
      message: "Minimum 5 overs not possible.",
      maxOversPerBowler: 0,
      powerplayOvers: 0,
    };
  }

  const ds_J = calculateMaxOversPerBowler(ds_I);
  const ds_L = Math.ceil(ds_I * OVER_RATE); // Innings length
  const ds_K_mins = startTimeMins + data.ds_C;
  const ds_M_mins = ds_K_mins + ds_L;
  const ds_N = 20 - ds_E_val; // Interval
  const ds_O_mins = ds_M_mins + ds_N;
  const ds_P_mins = ds_O_mins + ds_L;

  return {
    isValid: true,
    maxOvers: ds_I,
    startTime: minsToTime(ds_K_mins),
    endTime: minsToTime(ds_P_mins),
    interval: ds_N,
    maxOversPerBowler: ds_J,
    powerplayOvers: calculatePowerplay(ds_I),
  };
}

export function calculateInterruption1st(data: MatchData): CalculationResult {
  const startTimeMins = timeToMins(data.startTime);

  if (data.t20_scenario === 'interruption') {
    let i1_EE = 0;
    if (data.i1_CC > data.i1_DD) i1_EE = Math.min(10, data.i1_CC - data.i1_DD);

    const i1_FF = data.i1_CC - (data.i1_DD + i1_EE);
    const i1_GG = data.i1_AA - i1_FF;
    const i1_II = Math.min(20, Math.ceil(i1_GG / OVER_RATE / 2));

    if (i1_II < 5) {
      return {
        isValid: false,
        maxOvers: i1_II,
        message: "Minimum 5 overs not possible.",
        maxOversPerBowler: 0,
        powerplayOvers: 0,
      };
    }

    const i1_JJ = calculateMaxOversPerBowler(i1_II);
    const i1_LL = Math.ceil(i1_II * OVER_RATE);
    const i1_KK_mins = startTimeMins + data.i1_BB + data.i1_CC;
    const i1_MM_mins = i1_KK_mins + (i1_LL - data.i1_BB);
    const i1_NN = 20 - i1_EE;
    const i1_OO_mins = i1_MM_mins + i1_NN;
    const i1_PP_mins = i1_OO_mins + i1_LL;

    return {
      isValid: true,
      maxOvers: i1_II,
      startTime: minsToTime(i1_KK_mins),
      endTime: minsToTime(i1_PP_mins),
      interval: i1_NN,
      maxOversPerBowler: i1_JJ,
      powerplayOvers: calculatePowerplay(i1_II),
    };
  } else {
    // Terminate scenario
    const p_mins = timeToMins(data.term_P);
    const q_mins = timeToMins(data.term_Q);
    const r_val = q_mins - p_mins;
    const s_val = Math.ceil(r_val / OVER_RATE);

    if (s_val > data.term_T) {
      return {
        isValid: false,
        maxOvers: s_val,
        message: "Potential overs > overs faced. Revert to 'Interruption' calculation.",
        maxOversPerBowler: 0,
        powerplayOvers: 0,
      };
    }

    if (s_val < 5) {
      return {
        isValid: false,
        maxOvers: s_val,
        message: "Minimum 5 overs not possible.",
        maxOversPerBowler: 0,
        powerplayOvers: 0,
      };
    }

    const term_length = Math.ceil(s_val * OVER_RATE);
    const term_end_mins = p_mins + term_length;
    const term_bowler_max = calculateMaxOversPerBowler(s_val);

    return {
      isValid: true,
      maxOvers: s_val,
      startTime: minsToTime(p_mins),
      endTime: minsToTime(term_end_mins),
      maxOversPerBowler: term_bowler_max,
      powerplayOvers: calculatePowerplay(s_val),
      message: "Terminate 1st Innings. Revised HoP for 2nd Innings:",
    };
  }
}

export function calculateInterruption2nd(data: MatchData): CalculationResult {
  const i2_GGG = data.i2_EEE - data.i2_FFF; // Net time lost
  const i2_III = Math.max(0, Math.floor(i2_GGG / OVER_RATE)); // Overs lost
  const i2_JJJ = Math.min(20, data.i2_HHH - i2_III); // Adjusted max overs

  if (i2_JJJ < 5) {
    return {
      isValid: false,
      maxOvers: i2_JJJ,
      message: "Minimum 5 overs not possible.",
      maxOversPerBowler: 0,
      powerplayOvers: 0,
    };
  }

  const i2_KKK = Math.ceil(i2_JJJ * OVER_RATE); // Rescheduled length
  const i2_DDD_mins = timeToMins(data.i2_DDD);
  const i2_LLL_mins = i2_DDD_mins + (i2_KKK - data.i2_CCC);
  const i2_MMM = calculateMaxOversPerBowler(i2_JJJ);

  return {
    isValid: true,
    maxOvers: i2_JJJ,
    startTime: minsToTime(i2_DDD_mins),
    endTime: minsToTime(i2_LLL_mins),
    maxOversPerBowler: i2_MMM,
    powerplayOvers: calculatePowerplay(i2_JJJ),
  };
}
