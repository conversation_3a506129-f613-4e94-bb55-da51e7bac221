import React, { useState } from "react";
import {
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import { MatchData } from "../types";

interface SidebarProps {
  data: MatchData;
  onDataChange: (data: MatchData) => void;
}

interface AccordionItemProps {
  title: string;
  icon: string;
  children: React.ReactNode;
  isOpen: boolean;
  onToggle: () => void;
}

const AccordionItem: React.FC<AccordionItemProps> = ({
  title,
  icon,
  children,
  isOpen,
  onToggle,
}) => {
  return (
    <div className="border-b border-gray-200 dark:border-gray-700">
      <button
        className="accordion-button w-full flex justify-between items-center p-4 text-left font-semibold hover:bg-gray-100 dark:hover:bg-gray-700 transition"
        onClick={onToggle}
      >
        <span>
          {icon} {title}
        </span>
        <span className="transform transition-transform duration-300">
          {isOpen ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
        </span>
      </button>
      <div
        className={`accordion-content bg-gray-50 dark:bg-gray-800/50 p-4 ${
          isOpen ? "max-h-[1000px]" : "max-h-0"
        }`}
      >
        {children}
      </div>
    </div>
  );
};

export const Sidebar: React.FC<SidebarProps> = ({ data, onDataChange }) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [openAccordion, setOpenAccordion] = useState<string | null>(
    "match-info"
  );

  const handleInputChange = (field: keyof MatchData, value: any) => {
    onDataChange({ ...data, [field]: value });
  };

  const toggleAccordion = (id: string) => {
    setOpenAccordion(openAccordion === id ? null : id);
  };

  return (
    <aside
      className={`${
        isCollapsed ? "w-16" : "w-full lg:w-1/3 xl:w-1/4"
      } bg-white dark:bg-gray-800 shadow-lg transition-all duration-300 relative`}
    >
      <button
        onClick={() => setIsCollapsed(!isCollapsed)}
        className="absolute -right-3 top-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-full p-1 shadow-md hover:shadow-lg transition-shadow z-10"
      >
        {isCollapsed ? <ChevronRight size={16} /> : <ChevronLeft size={16} />}
      </button>

      {!isCollapsed && (
        <div className="p-6">
          <div className="flex items-center mb-6">
            <img
              src="https://placehold.co/80x80/0E46A3/white?text=MT"
              alt="Logo"
              className="rounded-full"
            />
            <h1 className="text-xl font-bold ml-4">
              T20 Revised Overs Calculator
            </h1>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
            This app helps in calculating revised overs in a Delayed or
            Interrupted game during Maharaja Trophy.
          </p>

          <div>
            <AccordionItem
              title="Match Information"
              icon="ℹ️"
              isOpen={openAccordion === "match-info"}
              onToggle={() => toggleAccordion("match-info")}
            >
              <div className="space-y-4">
                <div>
                  <label
                    className="block text-sm font-medium mb-2"
                    htmlFor="teams"
                  >
                    Teams for the match:
                  </label>
                  <input
                    type="text"
                    id="teams"
                    className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 border-gray-300 dark:border-gray-600"
                    value={data.teams}
                    onChange={(e) => handleInputChange("teams", e.target.value)}
                  />
                </div>
                <div>
                  <label
                    className="block text-sm font-medium mb-2"
                    htmlFor="starttime"
                  >
                    Start Time of the match:
                  </label>
                  <input
                    type="time"
                    id="starttime"
                    className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 border-gray-300 dark:border-gray-600"
                    value={data.startTime}
                    onChange={(e) =>
                      handleInputChange("startTime", e.target.value)
                    }
                  />
                </div>
              </div>
            </AccordionItem>

            <AccordionItem
              title="Cut-off time"
              icon="⏰"
              isOpen={openAccordion === "cutoff"}
              onToggle={() => toggleAccordion("cutoff")}
            >
              <div>
                <label className="block text-sm font-medium mb-2">
                  Select Cut-Off Type:
                </label>
                <div className="space-y-2">
                  <div>
                    <input
                      type="radio"
                      id="cutoff_game"
                      name="cutoff_scenario"
                      value="game"
                      checked={data.cutoffScenario === "game"}
                      onChange={(e) =>
                        handleInputChange("cutoffScenario", e.target.value)
                      }
                      className="mr-2"
                    />
                    <label htmlFor="cutoff_game">
                      To start the game: Minimum 5 overs a side
                    </label>
                  </div>
                  <div>
                    <input
                      type="radio"
                      id="cutoff_2nd"
                      name="cutoff_scenario"
                      value="2nd_innings"
                      checked={data.cutoffScenario === "2nd_innings"}
                      onChange={(e) =>
                        handleInputChange("cutoffScenario", e.target.value)
                      }
                      className="mr-2"
                    />
                    <label htmlFor="cutoff_2nd">
                      To start the 2nd innings: Minimum 5 overs
                    </label>
                  </div>
                </div>
              </div>
            </AccordionItem>

            <AccordionItem
              title="Delayed Start"
              icon="⏳"
              isOpen={openAccordion === "delayed-start"}
              onToggle={() => toggleAccordion("delayed-start")}
            >
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="ds_A" className="text-sm">
                      Net playing time (A)
                    </label>
                    <input
                      type="number"
                      id="ds_A"
                      value={data.ds_A}
                      onChange={(e) =>
                        handleInputChange("ds_A", Number(e.target.value))
                      }
                      className="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"
                    />
                  </div>
                  <div>
                    <label htmlFor="ds_C" className="text-sm">
                      Playing time lost (C)
                    </label>
                    <input
                      type="number"
                      id="ds_C"
                      value={data.ds_C}
                      onChange={(e) =>
                        handleInputChange("ds_C", Number(e.target.value))
                      }
                      className="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"
                    />
                  </div>
                  <div>
                    <label htmlFor="ds_D" className="text-sm">
                      Extra time available (D)
                    </label>
                    <input
                      type="number"
                      id="ds_D"
                      value={data.ds_D}
                      onChange={(e) =>
                        handleInputChange("ds_D", Number(e.target.value))
                      }
                      className="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"
                    />
                  </div>
                  <div>
                    <label htmlFor="ds_E" className="text-sm">
                      Time made up (E)
                    </label>
                    <input
                      type="number"
                      id="ds_E"
                      value={data.ds_E}
                      readOnly
                      className="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"
                    />
                  </div>
                </div>
                <div>
                  <label htmlFor="ds_interval_minutes" className="text-sm">
                    Interval (minutes)
                  </label>
                  <input
                    type="number"
                    id="ds_interval_minutes"
                    value={data.ds_interval_minutes}
                    onChange={(e) =>
                      handleInputChange(
                        "ds_interval_minutes",
                        Number(e.target.value)
                      )
                    }
                    className="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"
                  />
                </div>
              </div>
            </AccordionItem>

            <AccordionItem
              title="Interruption: 1st Innings"
              icon="🌧️"
              isOpen={openAccordion === "interruption-1st"}
              onToggle={() => toggleAccordion("interruption-1st")}
            >
              <div className="space-y-4">
                <div className="space-y-2 mb-4">
                  <div>
                    <input
                      type="radio"
                      id="interruption_1st"
                      name="t20_scenario"
                      value="interruption"
                      checked={data.t20_scenario === "interruption"}
                      onChange={(e) =>
                        handleInputChange("t20_scenario", e.target.value)
                      }
                      className="mr-2"
                    />
                    <label htmlFor="interruption_1st">
                      Interruption during 1st innings
                    </label>
                  </div>
                  <div>
                    <input
                      type="radio"
                      id="interruption_terminate"
                      name="t20_scenario"
                      value="terminate"
                      checked={data.t20_scenario === "terminate"}
                      onChange={(e) =>
                        handleInputChange("t20_scenario", e.target.value)
                      }
                      className="mr-2"
                    />
                    <label htmlFor="interruption_terminate">
                      Terminate the innings
                    </label>
                  </div>
                </div>

                {data.t20_scenario === "interruption" ? (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="i1_AA" className="text-sm">
                        Net playing time (A)
                      </label>
                      <input
                        type="number"
                        id="i1_AA"
                        value={data.i1_AA}
                        onChange={(e) =>
                          handleInputChange("i1_AA", Number(e.target.value))
                        }
                        className="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"
                      />
                    </div>
                    <div>
                      <label htmlFor="i1_BB" className="text-sm">
                        Time innings in progress (B)
                      </label>
                      <input
                        type="number"
                        id="i1_BB"
                        value={data.i1_BB}
                        onChange={(e) =>
                          handleInputChange("i1_BB", Number(e.target.value))
                        }
                        className="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"
                      />
                    </div>
                    <div>
                      <label htmlFor="i1_CC" className="text-sm">
                        Playing time lost (C)
                      </label>
                      <input
                        type="number"
                        id="i1_CC"
                        value={data.i1_CC}
                        onChange={(e) =>
                          handleInputChange("i1_CC", Number(e.target.value))
                        }
                        className="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"
                      />
                    </div>
                    <div>
                      <label htmlFor="i1_DD" className="text-sm">
                        Extra time available (D)
                      </label>
                      <input
                        type="number"
                        id="i1_DD"
                        value={data.i1_DD}
                        onChange={(e) =>
                          handleInputChange("i1_DD", Number(e.target.value))
                        }
                        className="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"
                      />
                    </div>
                    <div>
                      <label htmlFor="i1_EE" className="text-sm">
                        Time made up (E)
                      </label>
                      <input
                        type="number"
                        id="i1_EE"
                        value={data.i1_EE}
                        readOnly
                        className="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"
                      />
                    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="term_P" className="text-sm">
                        Proposed re-start (P)
                      </label>
                      <input
                        type="time"
                        id="term_P"
                        value={data.term_P}
                        onChange={(e) =>
                          handleInputChange("term_P", e.target.value)
                        }
                        className="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"
                      />
                    </div>
                    <div>
                      <label htmlFor="term_Q" className="text-sm">
                        Rescheduled cut-off (Q)
                      </label>
                      <input
                        type="time"
                        id="term_Q"
                        value={data.term_Q}
                        onChange={(e) =>
                          handleInputChange("term_Q", e.target.value)
                        }
                        className="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"
                      />
                    </div>
                    <div>
                      <label htmlFor="term_R" className="text-sm">
                        Mins between P & Q (R)
                      </label>
                      <input
                        type="number"
                        id="term_R"
                        value={data.term_R}
                        readOnly
                        className="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"
                      />
                    </div>
                    <div>
                      <label htmlFor="term_S" className="text-sm">
                        Potential overs (S)
                      </label>
                      <input
                        type="number"
                        id="term_S"
                        value={data.term_S}
                        readOnly
                        className="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"
                      />
                    </div>
                    <div>
                      <label htmlFor="term_T" className="text-sm">
                        Overs faced in 1st inns (T)
                      </label>
                      <input
                        type="number"
                        id="term_T"
                        value={data.term_T}
                        onChange={(e) =>
                          handleInputChange("term_T", Number(e.target.value))
                        }
                        className="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"
                      />
                    </div>
                  </div>
                )}
              </div>
            </AccordionItem>

            <AccordionItem
              title="Interruption: 2nd Innings"
              icon="🌧️"
              isOpen={openAccordion === "interruption-2nd"}
              onToggle={() => toggleAccordion("interruption-2nd")}
            >
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="i2_starttime" className="text-sm">
                      2nd Inns Start Time
                    </label>
                    <input
                      type="time"
                      id="i2_starttime"
                      value={data.i2_starttime}
                      onChange={(e) =>
                        handleInputChange("i2_starttime", e.target.value)
                      }
                      className="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"
                    />
                  </div>
                  <div>
                    <label htmlFor="i2_length" className="text-sm">
                      Length of 2nd Inns
                    </label>
                    <input
                      type="number"
                      id="i2_length"
                      value={data.i2_length}
                      onChange={(e) =>
                        handleInputChange("i2_length", Number(e.target.value))
                      }
                      className="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"
                    />
                  </div>
                  <div>
                    <label htmlFor="i2_CCC" className="text-sm">
                      Time innings in progress (C)
                    </label>
                    <input
                      type="number"
                      id="i2_CCC"
                      value={data.i2_CCC}
                      onChange={(e) =>
                        handleInputChange("i2_CCC", Number(e.target.value))
                      }
                      className="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"
                    />
                  </div>
                  <div>
                    <label htmlFor="i2_DDD" className="text-sm">
                      Restart time (D)
                    </label>
                    <input
                      type="time"
                      id="i2_DDD"
                      value={data.i2_DDD}
                      onChange={(e) =>
                        handleInputChange("i2_DDD", e.target.value)
                      }
                      className="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"
                    />
                  </div>
                  <div>
                    <label htmlFor="i2_EEE" className="text-sm">
                      Total playing time lost (E)
                    </label>
                    <input
                      type="number"
                      id="i2_EEE"
                      value={data.i2_EEE}
                      onChange={(e) =>
                        handleInputChange("i2_EEE", Number(e.target.value))
                      }
                      className="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"
                    />
                  </div>
                  <div>
                    <label htmlFor="i2_FFF" className="text-sm">
                      Additional time available (F)
                    </label>
                    <input
                      type="number"
                      id="i2_FFF"
                      value={data.i2_FFF}
                      onChange={(e) =>
                        handleInputChange("i2_FFF", Number(e.target.value))
                      }
                      className="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"
                    />
                  </div>
                  <div>
                    <label htmlFor="i2_HHH" className="text-sm">
                      Max overs at start of inns (H)
                    </label>
                    <input
                      type="number"
                      id="i2_HHH"
                      value={data.i2_HHH}
                      onChange={(e) =>
                        handleInputChange("i2_HHH", Number(e.target.value))
                      }
                      className="w-full p-2 border rounded mt-1 bg-gray-50 dark:bg-gray-700"
                    />
                  </div>
                </div>
              </div>
            </AccordionItem>
          </div>
        </div>
      )}
    </aside>
  );
};
