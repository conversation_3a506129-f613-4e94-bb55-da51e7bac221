export const OVER_RATE = 4.25;

export function timeToMins(timeStr: string): number {
  if (!timeStr) return 0;
  const [h, m] = timeStr.split(":").map(Number);
  return h * 60 + m;
}

export function minsToTime(mins: number): string {
  if (isNaN(mins)) return "00:00";
  const h = Math.floor(mins / 60) % 24;
  const m = Math.round(mins % 60);
  return `${String(h).padStart(2, "0")}:${String(m).padStart(2, "0")}`;
}

export function calculatePowerplay(overs: number): number {
  if (overs >= 19) return 6;
  if (overs >= 15) return 5;
  if (overs >= 12) return 4;
  if (overs >= 9) return 3;
  return 2;
}

export function calculateMaxOversPerBowler(overs: number): number {
  return overs > 9 ? Math.ceil(overs / 5) : 2;
}
