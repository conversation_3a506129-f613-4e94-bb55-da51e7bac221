export interface MatchData {
  teams: string;
  startTime: string;
  cutoffScenario: 'game' | '2nd_innings';
  
  // Delayed Start
  ds_A: number; // Net playing time
  ds_C: number; // Playing time lost
  ds_D: number; // Extra time available
  ds_E: number; // Time made up
  ds_interval_minutes: number;
  
  // Interruption 1st Innings
  t20_scenario: 'interruption' | 'terminate';
  i1_AA: number; // Net playing time
  i1_BB: number; // Time innings in progress
  i1_CC: number; // Playing time lost
  i1_DD: number; // Extra time available
  i1_EE: number; // Time made up
  
  // Terminate scenario
  term_P: string; // Proposed re-start time
  term_Q: string; // Rescheduled cut-off time
  term_R: number; // Minutes between P & Q
  term_S: number; // Potential overs
  term_T: number; // Overs faced in 1st innings
  
  // Interruption 2nd Innings
  i2_starttime: string; // 2nd innings start time
  i2_length: number; // Length of 2nd innings
  i2_CCC: number; // Time innings in progress
  i2_DDD: string; // Restart time
  i2_EEE: number; // Total playing time lost
  i2_FFF: number; // Additional time available
  i2_HHH: number; // Max overs at start of innings
}

export interface CalculationResult {
  isValid: boolean;
  maxOvers: number;
  startTime?: string;
  endTime?: string;
  interval?: number;
  maxOversPerBowler: number;
  powerplayOvers: number;
  message?: string;
}
