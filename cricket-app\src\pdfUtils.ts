import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

export async function downloadPDF(elementId: string, filename: string, teams: string) {
  const element = document.getElementById(elementId);
  if (!element) return;

  const date = new Date().toLocaleDateString("en-GB");

  try {
    const canvas = await html2canvas(element, {
      scale: 2,
      backgroundColor: document.documentElement.classList.contains("dark") ? "#1f2937" : "#ffffff",
    });

    const imgData = canvas.toDataURL("image/png");
    const pdf = new jsPDF({
      orientation: "p",
      unit: "mm",
      format: "a4",
    });

    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = pdf.internal.pageSize.getHeight();
    const cardWidth = canvas.width;
    const cardHeight = canvas.height;
    const ratio = cardWidth / cardHeight;

    let imgWidth = pdfWidth - 20;
    let imgHeight = imgWidth / ratio;

    const yPos = 40;

    pdf.setFontSize(16);
    pdf.setFont("helvetica", "bold");
    pdf.text(`Revised Hours of Play: ${teams}`, pdfWidth / 2, 20, {
      align: "center",
    });
    pdf.setFontSize(12);
    pdf.setFont("helvetica", "normal");
    pdf.text(`Date: ${date}`, pdfWidth / 2, 28, { align: "center" });

    pdf.addImage(imgData, "PNG", 10, yPos, imgWidth, imgHeight);

    // Add dotted line and repeat content for distribution
    let currentY = yPos + imgHeight + 10;
    const repeatCount = 3;

    for (let i = 0; i < repeatCount; i++) {
      if (currentY > pdfHeight - 20 - imgHeight) {
        pdf.addPage();
        currentY = 20;
      }
      pdf.setLineDashPattern([1, 1], 0);
      pdf.line(10, currentY, pdfWidth - 10, currentY);
      currentY += 10;
      pdf.addImage(imgData, "PNG", 10, currentY, imgWidth, imgHeight);
      currentY += imgHeight + 10;
    }

    pdf.save(`${filename} ${date}.pdf`);
  } catch (error) {
    console.error('Error generating PDF:', error);
  }
}
